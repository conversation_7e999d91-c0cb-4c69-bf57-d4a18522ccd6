Imports System.Data.OleDb

Partial Public Class OtpVerification
    Inherits System.Web.UI.Page

    Private emailServiceClient As EmailServiceClient

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Initialize email service client
        Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
        If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
        emailServiceClient = New EmailServiceClient(serviceUrl)

        ' Check if user has valid temporary session
        If Session("TEMP_USER_ID") Is Nothing Then
            'Response.Redirect("p0_Login.aspx")
            Response.Redirect("blank.aspx")
            Return
        End If

        If Not IsPostBack Then
            ' Generate and send OTP on first load
            GenerateAndSendOtp()
        End If
    End Sub

    Private Sub GenerateAndSendOtp()
        Try
            Dim userId As String = Session("TEMP_USER_ID").ToString()
            
            ' Get user's email
            Dim userEmail As String = GetUserEmail(userId)
            If String.IsNullOrEmpty(userEmail) Then
                ShowMessage("Alamat email tidak dijumpai untuk akaun ini", "danger")
                Return
            End If

            ' Check if email service is available
            If Not emailServiceClient.CheckHealth() Then
                ShowMessage("Sistem OTP tidak tersedia. Log masuk tanpa OTP...", "info")
                ' Fallback to normal login
                CompleteLogin()
                Return
            End If

            ' Generate OTP
            Dim response = emailServiceClient.GenerateOTP(userId, userEmail, "LOGIN")
            
            If response.Success Then
                ShowMessage("Kod OTP telah dihantar ke email anda: " & MaskEmail(userEmail), "success")
            Else
                ShowMessage("Gagal menghantar OTP: " & response.Message & ". Log masuk tanpa OTP...", "info")
                ' Fallback to normal login
                CompleteLogin()
            End If

        Catch ex As Exception
            ShowMessage("Ralat sistem OTP: " & ex.Message & ". Log masuk tanpa OTP...", "info")
            ' Fallback to normal login
            CompleteLogin()
        End Try
    End Sub

    Protected Sub ButtonVerifyOtp_Click(sender As Object, e As EventArgs) Handles ButtonVerifyOtp.Click
        Try
            If String.IsNullOrEmpty(TextBoxOtp.Text) OrElse TextBoxOtp.Text.Trim() = "" Then
                ShowMessage("Sila masukkan kod OTP", "danger")
                TextBoxOtp.Focus()
                Return
            End If

            If TextBoxOtp.Text.Length <> 6 OrElse Not IsNumeric(TextBoxOtp.Text) Then
                ShowMessage("Kod OTP mestilah 6 digit nombor", "danger")
                TextBoxOtp.Focus()
                Return
            End If

            Dim userId As String = Session("TEMP_USER_ID").ToString()
            
            ' Validate OTP
            Dim response = emailServiceClient.ValidateOTP(userId, TextBoxOtp.Text, "LOGIN")
            
            If response.Success Then
                ' OTP is valid, complete login
                CompleteLogin()
            Else
                ShowMessage(response.Message, "danger")
                TextBoxOtp.Text = ""
                TextBoxOtp.Focus()
            End If

        Catch ex As Exception
            ShowMessage("Ralat sistem: " & ex.Message, "danger")
        End Try
    End Sub

    Protected Sub ButtonResendOtp_Click(sender As Object, e As EventArgs) Handles ButtonResendOtp.Click
        Try
            ' Clear any existing message
            PanelMessage.Visible = False
            
            ' Generate new OTP
            GenerateAndSendOtp()
            
            ' Clear OTP input
            TextBoxOtp.Text = ""
            TextBoxOtp.Focus()

        Catch ex As Exception
            ShowMessage("Ralat sistem: " & ex.Message, "danger")
        End Try
    End Sub

    Private Sub CompleteLogin()
        Try
            ' Move temporary session data to permanent session
            Session("Id_PG") = Session("TEMP_USER_ID")
            Session("PWD") = ""
            Session("MODUL") = Session("TEMP_MODUL")
            Session("AKSES") = Session("TEMP_AKSES")
            Session("ORIGIN") = Session("TEMP_ORIGIN")

            ' Check if user needs to change password after OTP verification
            Dim forcePasswordChange As Boolean = False
            If Session("FORCE_PASSWORD_CHANGE") IsNot Nothing Then
                forcePasswordChange = Convert.ToBoolean(Session("FORCE_PASSWORD_CHANGE"))
            End If

            ' Clear temporary session data
            Session("TEMP_USER_ID") = Nothing
            Session("TEMP_MODUL") = Nothing
            Session("TEMP_AKSES") = Nothing
            Session("TEMP_ORIGIN") = Nothing

            ' Redirect based on whether password change is required
            If forcePasswordChange Then
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION: User needs password change after OTP verification")
                Response.Redirect("p0_PasswordChangeForced.aspx")
            Else
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION: Normal login completed after OTP verification")
                Response.Redirect("blank.aspx")
            End If

        Catch ex As Exception
            ShowMessage("Ralat melengkapkan log masuk: " & ex.Message, "danger")
        End Try
    End Sub

    Private Function GetUserEmail(userId As String) As String
        Try
            Dim email As String = ""
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand
            
            Cn.ConnectionString = SPMJ_Mod.ServerId
            Cn.Open()
            Cmd.Connection = Cn
            Cmd.CommandText = "SELECT email FROM pn_pengguna WHERE id_pg = ? AND status = '1'"
            Cmd.Parameters.AddWithValue("@id_pg", userId)
            
            Dim result = Cmd.ExecuteScalar()
            If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                email = result.ToString().Trim()
            End If
            
            Cn.Close()
            Return email
        Catch
            Return ""
        End Try
    End Function

    Private Function MaskEmail(email As String) As String
        Try
            If String.IsNullOrEmpty(email) OrElse Not email.Contains("@") Then
                Return email
            End If

            Dim parts = email.Split("@"c)
            Dim localPart = parts(0)
            Dim domainPart = parts(1)

            If localPart.Length <= 2 Then
                Return email
            End If

            ' Show first and last character of local part, mask the middle
            Dim masked = localPart.Substring(0, 1) & 
                        New String("*"c, localPart.Length - 2) & 
                        localPart.Substring(localPart.Length - 1, 1) & 
                        "@" & domainPart

            Return masked
        Catch
            Return email
        End Try
    End Function

    Private Sub ShowMessage(message As String, type As String)
        LabelMessage.Text = message
        PanelMessage.Visible = True
        
        ' Set CSS class based on type
        Select Case type.ToLower()
            Case "success"
                alertDiv.Attributes("class") = "alert alert-success"
            Case "danger", "error"
                alertDiv.Attributes("class") = "alert alert-danger"
            Case "info"
                alertDiv.Attributes("class") = "alert alert-info"
            Case Else
                alertDiv.Attributes("class") = "alert alert-info"
        End Select
    End Sub

End Class
