Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Data

Partial Public Class WebForm30
    Inherits System.Web.UI.Page

    ' Email service client for OTP functionality
    Private emailServiceClient As EmailServiceClient

    ' Password recovery email toggle state
    Private Property IsEmailVisible As Boolean
        Get
            Return If(ViewState("IsEmailVisible"), False)
        End Get
        Set(value As Boolean)
            ViewState("IsEmailVisible") = value
        End Set
    End Property

    Private Property FullEmailAddress As String
        Get
            Return If(ViewState("FullEmailAddress"), "")
        End Get
        Set(value As String)
            ViewState("FullEmailAddress") = value
        End Set
    End Property

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        ' Initialize email service client for OTP functionality
        Try
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            emailServiceClient = New EmailServiceClient(serviceUrl)
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Email service client initialized with URL: " & serviceUrl)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Failed to initialize email service client: " & ex.Message)
            ' Continue without email service - OTP will be disabled
        End Try

        Dim x As System.Web.UI.WebControls.Menu = Master.FindControl("Menu1")
        x.Visible = False
    End Sub

    Protected Sub cmd_OK_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_OK.Click
        System.Diagnostics.Debug.WriteLine("=== LOGIN ATTEMPT STARTED ===")
        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: User ID: " & Tx_Id.Text)
        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Password length: " & Tx_Pwd.Text.Length)

        If Tx_Id.Text.Trim = "" Then
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Empty user ID")
            Tx_Id.Focus() : Exit Sub
        End If
        If Tx_Pwd.Text.Trim = "" Then
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Empty password")
            Tx_Pwd.Focus() : Exit Sub
        End If

        If Chk_SQL(Tx_Id.Text) = True Then
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: SQL injection detected in user ID")
            Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!") : Exit Sub
        End If
        If Chk_SQL(Tx_Pwd.Text) = True Then
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: SQL injection detected in password")
            Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!") : Exit Sub
        End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Try
            ' First, get user information
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Executing database query for user: " & Tx_Id.Text)
            Cmd.CommandText = "select * from pn_pengguna where id_pg = '" & Tx_Id.Text & "' and status=1"
            Rdr = Cmd.ExecuteReader()

            If Rdr.Read Then
                System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: User found in database")
                Dim storedPassword As String = Rdr("pwd").ToString()
                Dim storedSalt As String = If(IsDBNull(Rdr("salt")), "", Rdr("salt").ToString())
                Dim passwordMigrated As Boolean = If(IsDBNull(Rdr("password_migrated")), False, CBool(Rdr("password_migrated")))
                Dim isTemporary As Boolean = If(IsDBNull(Rdr("is_temporary")), False, CBool(Rdr("is_temporary")))
                Dim forceChange As Boolean = If(IsDBNull(Rdr("force_change")), False, CBool(Rdr("force_change")))
                Dim userId As String = Rdr("Id_PG").ToString()
                Dim userModul As String = Rdr("MODUL").ToString()
                Dim userAkses As String = Rdr("AKSES").ToString()

                System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: User " & userId & " - passwordMigrated: " & passwordMigrated.ToString() & ", isTemporary: " & isTemporary.ToString() & ", forceChange: " & forceChange.ToString())

                Rdr.Close()

                Dim isValidLogin As Boolean = False
                Dim needsPasswordUpdate As Boolean = False

                If passwordMigrated And Not String.IsNullOrEmpty(storedSalt) Then
                    ' User has encrypted password - verify using hash
                    System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: User " & userId & " has encrypted password")
                    System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Stored password length: " & storedPassword.Length)
                    System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Stored salt length: " & storedSalt.Length)
                    System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Input password: " & Tx_Pwd.Text)

                    isValidLogin = PasswordHelper.VerifyPasswordWithFallback(Tx_Pwd.Text, storedPassword, storedSalt)
                    System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Password verification result: " & isValidLogin.ToString())
                Else
                    ' User has plain text password - check directly and migrate
                    System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: User " & userId & " has plain text password")
                    If storedPassword = Tx_Pwd.Text Then
                        isValidLogin = True
                        needsPasswordUpdate = True
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Plain text password match - will migrate")
                    Else
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Plain text password no match")
                    End If
                End If

                If isValidLogin Then
                    System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Password validation successful for user " & userId)

                    ' Check if user still needs to change password
                    If (isTemporary Or forceChange) Then
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: User still needs password change - isTemporary: " & isTemporary.ToString() & ", forceChange: " & forceChange.ToString())
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Redirecting to forced change page")
                        ' Set session and redirect to password change page
                        Session("Id_PG") = userId
                        Session("PWD") = ""  ' Clear plain text password from session
                        Session("MODUL") = userModul
                        Session("AKSES") = userAkses
                        Session("ORIGIN") = "yes"
                        Session("FORCE_PASSWORD_CHANGE") = "true"

                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: About to redirect to p0_PasswordChangeForced.aspx (temporary password)")
                        Try
                            Response.Redirect("p0_PasswordChangeForced.aspx")
                            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Redirect to password change completed (temporary)")
                        Catch ex As Exception
                            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Redirect to password change failed (temporary): " & ex.Message)
                            Throw ex
                        End Try
                    ElseIf needsPasswordUpdate Then
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Plain text password needs migration")
                        ' Migrate password to encrypted version
                        MigrateUserPassword(userId, Tx_Pwd.Text, Cn)

                        ' After migration, check if OTP is required before proceeding
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Password migrated, checking OTP requirement")

                        ' Get user's email for OTP
                        Dim userEmail As String = GetUserEmail(userId)

                        ' Check OTP configuration
                        Dim otpEnabled As Boolean = True
                        Try
                            Dim otpSetting As String = System.Configuration.ConfigurationManager.AppSettings("OtpEnabled")
                            If Not String.IsNullOrEmpty(otpSetting) AndAlso otpSetting.ToLower() = "false" Then
                                otpEnabled = False
                                System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: OTP disabled via configuration")
                            End If
                        Catch
                            ' Default to enabled if configuration not found
                        End Try

                        Dim otpRequired As Boolean = otpEnabled AndAlso Not String.IsNullOrEmpty(userEmail) AndAlso userEmail.Length > 5 AndAlso userEmail.Contains("@")
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: After migration - OTP required: " & otpRequired.ToString())

                        If otpRequired Then
                            ' Try to generate OTP via microservice
                            Try
                                If emailServiceClient IsNot Nothing AndAlso emailServiceClient.CheckHealth() Then
                                    Dim otpResponse = emailServiceClient.GenerateOTP(userId, userEmail, "LOGIN")

                                    If otpResponse.Success Then
                                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: OTP generated for migrated user, redirecting to OTP verification")

                                        ' Store login session temporarily and redirect to OTP verification
                                        Session("TEMP_USER_ID") = userId
                                        Session("TEMP_MODUL") = userModul
                                        Session("TEMP_AKSES") = userAkses
                                        Session("TEMP_ORIGIN") = "yes"
                                        Session("USER_EMAIL") = userEmail
                                        Session("FORCE_PASSWORD_CHANGE") = "true"  ' Still need password change after OTP

                                        Response.Redirect("OtpVerification.aspx", True)
                                        Return
                                    End If
                                End If
                            Catch ex As Exception
                                System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: OTP generation failed for migrated user: " & ex.Message)
                            End Try
                        End If

                        ' Fallback: Set session and redirect to password change page (no OTP or OTP failed)
                        Session("Id_PG") = userId
                        Session("PWD") = ""  ' Clear plain text password from session
                        Session("MODUL") = userModul
                        Session("AKSES") = userAkses
                        Session("ORIGIN") = "yes"
                        Session("FORCE_PASSWORD_CHANGE") = "true"

                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: About to redirect to p0_PasswordChangeForced.aspx (migration)")
                        Try
                            Response.Redirect("p0_PasswordChangeForced.aspx")
                            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Redirect to password change completed (migration)")
                        Catch ex As Exception
                            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Redirect to password change failed (migration): " & ex.Message)
                            Throw ex
                        End Try
                    Else
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Normal login proceeding for user with encrypted password")
                        ' Normal login for users with encrypted passwords
                        Session("Id_PG") = userId
                        Session("PWD") = ""  ' Don't store password in session anymore
                        Session("MODUL") = userModul
                        Session("AKSES") = userAkses
                        Session("ORIGIN") = "yes"

                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Checking if OTP is required for user " & userId)
                        ' Check if OTP is enabled for this user (check if user has email)
                        Dim userEmail As String = GetUserEmail(userId)

                        ' Check OTP configuration
                        Dim otpEnabled As Boolean = True
                        Try
                            Dim otpSetting As String = System.Configuration.ConfigurationManager.AppSettings("OtpEnabled")
                            If Not String.IsNullOrEmpty(otpSetting) AndAlso otpSetting.ToLower() = "false" Then
                                otpEnabled = False
                                System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: OTP disabled via configuration")
                            End If
                        Catch
                            ' Default to enabled if configuration not found
                        End Try

                        ' Enhanced OTP requirement check - make OTP mandatory for all encrypted password users with valid email
                        Dim otpRequired As Boolean = otpEnabled AndAlso Not String.IsNullOrEmpty(userEmail) AndAlso userEmail.Length > 5 AndAlso userEmail.Contains("@")
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: User email: '" & userEmail & "'")
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: OTP enabled: " & otpEnabled.ToString())
                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: OTP required: " & otpRequired.ToString())

                        ' Log security event for encrypted password login attempt
                        LogSecurityEvent("ENCRYPTED_PASSWORD_LOGIN_ATTEMPT", userId, "OTP Required: " & otpRequired.ToString())
                        If otpRequired Then
                            ' Try to generate OTP via microservice first
                            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Attempting to generate OTP via microservice")

                            Try
                                If emailServiceClient IsNot Nothing Then
                                    ' Check if email service is available
                                    If emailServiceClient.CheckHealth() Then
                                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Email service is healthy, generating OTP")

                                        ' Generate OTP
                                        Dim otpResponse = emailServiceClient.GenerateOTP(userId, userEmail, "LOGIN")

                                        If otpResponse.Success Then
                                            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: OTP generated successfully")

                                            ' Store login session temporarily and redirect to OTP verification
                                            Session("TEMP_USER_ID") = userId
                                            Session("TEMP_MODUL") = userModul
                                            Session("TEMP_AKSES") = userAkses
                                            Session("TEMP_ORIGIN") = "yes"
                                            Session("USER_EMAIL") = userEmail

                                            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Redirecting to OtpVerification.aspx")
                                            Response.Redirect("OtpVerification.aspx", True)
                                        Else
                                            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: OTP generation failed: " & otpResponse.Message)
                                            ' Fallback to normal login
                                            CompleteNormalLogin(userId, userModul, userAkses)
                                        End If
                                    Else
                                        System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Email service not healthy, falling back to normal login")
                                        ' Fallback to normal login
                                        CompleteNormalLogin(userId, userModul, userAkses)
                                    End If
                                Else
                                    System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Email service client not initialized, falling back to normal login")
                                    ' Fallback to normal login
                                    CompleteNormalLogin(userId, userModul, userAkses)
                                End If
                            Catch ex As Exception
                                System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Exception during OTP generation: " & ex.Message)
                                ' Fallback to normal login
                                CompleteNormalLogin(userId, userModul, userAkses)
                            End Try
                        Else
                            ' Normal login for users without email/OTP
                            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: No OTP required - email: '" & userEmail & "'")
                            CompleteNormalLogin(userId, userModul, userAkses)
                        End If
                    End If
                Else
                    System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Password validation FAILED for user " & userId)
                    Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!")
                    Exit Sub
                End If
            Else
                System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: User NOT found in database or status != 1")
                Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!")
                Exit Sub
            End If

            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Closing database connection")
            Cn.Close()
            System.Diagnostics.Debug.WriteLine("=== LOGIN ATTEMPT COMPLETED ===")
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Exception occurred: " & ex.Message)
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Exception stack trace: " & ex.StackTrace)
            If Cn.State = Data.ConnectionState.Open Then Cn.Close()
            'Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!!!")
            Msg(Me, ex.Message)
        End Try
    End Sub
    ''' <summary>
    ''' Get user's email address for OTP functionality
    ''' </summary>
    Private Function GetUserEmail(userId As String) As String
        Try
            Dim email As String = ""
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand

            System.Diagnostics.Debug.WriteLine("EMAIL DEBUG: Getting email for user: " & userId)

            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            Cmd.CommandText = "SELECT email FROM pn_pengguna WHERE id_pg = ? AND status = 1"
            Cmd.Parameters.AddWithValue("@id_pg", userId)

            Dim result = Cmd.ExecuteScalar()
            If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                email = result.ToString().Trim()
                System.Diagnostics.Debug.WriteLine("EMAIL DEBUG: Found email: " & email)
            Else
                System.Diagnostics.Debug.WriteLine("EMAIL DEBUG: No email found or email is NULL")
            End If

            Cn.Close()
            System.Diagnostics.Debug.WriteLine("EMAIL DEBUG: Returning email: '" & email & "' (Length: " & email.Length & ")")
            Return email
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("EMAIL DEBUG: Error getting email: " & ex.Message)
            Return ""
        End Try
    End Function

    ''' <summary>
    ''' Migrate user password from plain text to encrypted version
    ''' </summary>
    Private Sub MigrateUserPassword(userId As String, plainPassword As String, cn As OleDbConnection)
        Try
            Dim passwordEntry As String() = PasswordHelper.CreatePasswordEntry(plainPassword)
            Dim hashedPassword As String = passwordEntry(0)
            Dim salt As String = passwordEntry(1)

            Dim cmd As New OleDbCommand()
            cmd.Connection = cn
            cmd.CommandText = "UPDATE pn_pengguna SET pwd = ?, salt = ?, password_migrated = 1 WHERE id_pg = ?"
            cmd.Parameters.AddWithValue("@pwd", hashedPassword)
            cmd.Parameters.AddWithValue("@salt", salt)
            cmd.Parameters.AddWithValue("@id_pg", userId)

            cmd.ExecuteNonQuery()

        Catch ex As Exception
            ' Log error but don't prevent login
            ' In production, you should log this properly
        End Try
    End Sub

    ''' <summary>
    ''' Handle forgot password link click
    ''' </summary>
    Protected Sub lnk_ForgotPassword_Click(sender As Object, e As EventArgs) Handles lnk_ForgotPassword.Click
        System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Forgot password link clicked")

        ' Show the password recovery panel
        pnl_PasswordRecovery.Visible = True
        lbl_RecoveryMessage.Text = ""
        txt_RecoveryUserId.Text = ""
        pnl_EmailToggle.Visible = False

        ' Reset toggle state
        IsEmailVisible = False
        FullEmailAddress = ""

        txt_RecoveryUserId.Focus()

        System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Recovery panel displayed")
    End Sub

    ''' <summary>
    ''' Handle send recovery button click
    ''' </summary>
    Protected Sub btn_SendRecovery_Click(sender As Object, e As EventArgs) Handles btn_SendRecovery.Click
        System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Send recovery button clicked")

        Try
            ' Validate input
            If String.IsNullOrEmpty(txt_RecoveryUserId.Text.Trim()) Then
                lbl_RecoveryMessage.Text = "<span style='color: red;'>Sila masukkan ID Pengguna.</span>"
                txt_RecoveryUserId.Focus()
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Empty user ID")
                Return
            End If

            Dim userId As String = txt_RecoveryUserId.Text.Trim()

            ' Check for SQL injection
            If Chk_SQL(userId) Then
                lbl_RecoveryMessage.Text = "<span style='color: red;'>ID Pengguna tidak sah.</span>"
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: SQL injection detected in user ID")
                Return
            End If

            System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Processing recovery for user: " & userId)

            ' Check if user exists and get email
            Dim userExists As Boolean = False
            Dim userEmail As String = ""
            Dim userName As String = ""

            Dim cn As New OleDbConnection()
            Dim cmd As New OleDbCommand()

            Try
                cn.ConnectionString = ServerId
                cn.Open()
                cmd.Connection = cn
                cmd.CommandText = "SELECT nama, email FROM pn_pengguna WHERE id_pg = ? AND status = 1"
                cmd.Parameters.AddWithValue("@id_pg", userId)

                Dim reader As OleDbDataReader = cmd.ExecuteReader()
                If reader.Read() Then
                    userExists = True
                    userName = If(IsDBNull(reader("nama")), "", reader("nama").ToString())
                    userEmail = If(IsDBNull(reader("email")), "", reader("email").ToString().Trim())
                    System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: User found - Name: " & userName & ", Email: " & userEmail)
                End If
                reader.Close()

            Finally
                If cn.State = Data.ConnectionState.Open Then cn.Close()
            End Try

            If Not userExists Then
                lbl_RecoveryMessage.Text = "<span style='color: red;'>ID Pengguna tidak dijumpai.</span>"
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: User not found")
                Return
            End If

            If String.IsNullOrEmpty(userEmail) OrElse Not userEmail.Contains("@") Then
                lbl_RecoveryMessage.Text = "<span style='color: red;'>Tiada alamat email yang sah untuk akaun ini. Sila hubungi pentadbir sistem.</span>"
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: No valid email found for user")
                Return
            End If

            ' Initialize email service client
            Dim serviceUrl As String = System.Configuration.ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"

            System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Using email service URL: " & serviceUrl)

            Dim emailClient As New EmailServiceClient(serviceUrl)

            ' Check email service health - REQUIRED for operation
            If Not emailClient.CheckHealth() Then
                lbl_RecoveryMessage.Text = "<span style='color: red;'>Sistem email tidak tersedia pada masa ini. Sila cuba lagi kemudian.</span>"
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email service not available")
                Return
            End If

            System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email service health check passed")

            ' Generate temporary password
            Dim tempPassword As String = GenerateTemporaryPassword()
            System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Generated temporary password")

            ' Update user with temporary password
            Try
                cn.ConnectionString = ServerId
                cn.Open()
                cmd.Connection = cn

                ' Create encrypted password entry
                Dim passwordEntry As String() = PasswordHelper.CreatePasswordEntry(tempPassword)
                Dim hashedPassword As String = passwordEntry(0)
                Dim salt As String = passwordEntry(1)

                cmd.CommandText = "UPDATE pn_pengguna SET pwd = ?, salt = ?, password_migrated = 1, is_temporary = 1, force_change = 1, tarikh_tukar_katalaluan = ? WHERE id_pg = ?"
                cmd.Parameters.Clear()
                cmd.Parameters.AddWithValue("@pwd", hashedPassword)
                cmd.Parameters.AddWithValue("@salt", salt)
                cmd.Parameters.AddWithValue("@date", DateTime.Now)
                cmd.Parameters.AddWithValue("@id_pg", userId)

                Dim rowsAffected As Integer = cmd.ExecuteNonQuery()

                If rowsAffected > 0 Then
                    System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Database updated successfully")
                Else
                    Throw New Exception("Failed to update user password in database")
                End If

            Finally
                If cn.State = Data.ConnectionState.Open Then cn.Close()
            End Try

            ' Send password reset email via microservice (email-only mode)
            Dim response = emailClient.SendPasswordResetEmail(userId, userEmail, userName, tempPassword)

            If response.Success Then
                ' Store the full email address for toggle functionality
                FullEmailAddress = userEmail
                IsEmailVisible = False

                ' Show success message with masked email
                lbl_RecoveryMessage.Text = "<span style='color: green;'>Kata laluan sementara telah dihantar ke email anda: " & MaskEmail(userEmail) & "</span>"
                txt_RecoveryUserId.Text = ""

                ' Show the email toggle panel
                pnl_EmailToggle.Visible = True
                btn_ToggleEmail.Text = "👁️ Tunjuk Email"

                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email sent successfully via microservice")
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Toggle panel enabled for email: " & MaskEmail(userEmail))
            Else
                lbl_RecoveryMessage.Text = "<span style='color: red;'>Gagal menghantar email: " & response.Message & "</span>"
                pnl_EmailToggle.Visible = False
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email sending failed: " & response.Message)
            End If

        Catch ex As Exception
            lbl_RecoveryMessage.Text = "<span style='color: red;'>Ralat sistem: " & ex.Message & "</span>"
            System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Exception occurred: " & ex.Message)
            System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Exception stack trace: " & ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' Handle cancel recovery button click
    ''' </summary>
    Protected Sub btn_CancelRecovery_Click(sender As Object, e As EventArgs) Handles btn_CancelRecovery.Click
        System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Cancel recovery button clicked")

        ' Hide the password recovery panel
        pnl_PasswordRecovery.Visible = False
        lbl_RecoveryMessage.Text = ""
        txt_RecoveryUserId.Text = ""
        pnl_EmailToggle.Visible = False

        ' Reset toggle state
        IsEmailVisible = False
        FullEmailAddress = ""

        System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Recovery panel hidden")
    End Sub

    ''' <summary>
    ''' Handle email toggle button click
    ''' </summary>
    Protected Sub btn_ToggleEmail_Click(sender As Object, e As EventArgs) Handles btn_ToggleEmail.Click
        System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email toggle button clicked")

        Try
            If String.IsNullOrEmpty(FullEmailAddress) Then
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: No email address to toggle")
                Return
            End If

            ' Toggle the email visibility state
            IsEmailVisible = Not IsEmailVisible

            If IsEmailVisible Then
                ' Show full email
                lbl_RecoveryMessage.Text = lbl_RecoveryMessage.Text.Replace(MaskEmail(FullEmailAddress), FullEmailAddress)
                btn_ToggleEmail.Text = "🙈 Sembunyi Email"
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email shown - " & FullEmailAddress)
            Else
                ' Hide email (mask it)
                lbl_RecoveryMessage.Text = lbl_RecoveryMessage.Text.Replace(FullEmailAddress, MaskEmail(FullEmailAddress))
                btn_ToggleEmail.Text = "👁️ Tunjuk Email"
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email hidden - " & MaskEmail(FullEmailAddress))
            End If

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Toggle email exception: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Generate a temporary password
    ''' </summary>
    Private Function GenerateTemporaryPassword() As String
        Dim chars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        Dim random As New Random()
        Dim result As New System.Text.StringBuilder()

        For i As Integer = 0 To 7 ' 8 character password
            result.Append(chars(random.Next(chars.Length)))
        Next

        Return result.ToString()
    End Function

    ''' <summary>
    ''' Mask email address for display
    ''' </summary>
    Private Function MaskEmail(email As String) As String
        Try
            If String.IsNullOrEmpty(email) OrElse Not email.Contains("@") Then
                Return email
            End If

            Dim parts = email.Split("@"c)
            Dim localPart = parts(0)
            Dim domainPart = parts(1)

            If localPart.Length <= 2 Then
                Return email
            End If

            ' Show first and last character of local part, mask the middle
            Dim masked = localPart.Substring(0, 1) &
                    New String("*"c, localPart.Length - 2) &
                    localPart.Substring(localPart.Length - 1, 1) &
                    "@" & domainPart

            Return masked
        Catch
            Return email
        End Try
    End Function

    ''' <summary>
    ''' Complete normal login without OTP
    ''' </summary>
    Private Sub CompleteNormalLogin(userId As String, userModul As String, userAkses As String)
        Try
            ' Set session variables for normal login
            Session("Id_PG") = userId
            Session("PWD") = ""  ' Don't store password in session anymore
            Session("MODUL") = userModul
            Session("AKSES") = userAkses
            Session("ORIGIN") = "yes"

            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Normal login completed for user: " & userId)
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Redirecting to blank.aspx")

            ' Redirect to main application
            Response.Redirect("blank.aspx")

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("LOGIN DEBUG: Error in CompleteNormalLogin: " & ex.Message)
            Msg(Me, "Ralat sistem semasa log masuk. Sila cuba lagi.")
        End Try
    End Sub

    ''' <summary>
    ''' Log security events for audit trail
    ''' </summary>
    Private Sub LogSecurityEvent(eventType As String, userId As String, Optional details As String = "")
        Try
            Dim logMessage As String = String.Format("[{0}] {1} - User: {2} - {3}",
                                                    DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                                    eventType,
                                                    userId,
                                                    details)
            System.Diagnostics.Debug.WriteLine("SECURITY LOG: " & logMessage)

            ' TODO: In production, also log to database or file for audit trail
            ' For now, just debug output
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("SECURITY LOG ERROR: " & ex.Message)
        End Try
    End Sub
End Class
